/* 智能网页总结助手 - 侧边栏样式 */

:root {
  /* 深色主题色彩 */
  --bg-primary: #0f0f23;
  --bg-secondary: #1a1a2e;
  --bg-tertiary: #16213e;
  --bg-card: #1e1e3f;
  --bg-hover: #2a2a4a;
  
  /* 渐变色 */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  
  /* 文字颜色 */
  --text-primary: #ffffff;
  --text-secondary: #b8b8d1;
  --text-muted: #8b8ba7;
  --text-accent: #64ffda;
  
  /* 边框和分割线 */
  --border-color: #2a2a4a;
  --border-radius: 12px;
  --border-radius-sm: 8px;
  
  /* 阴影 */
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.15);
  --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.3);
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* 动画 */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* 浅色主题 */
[data-theme="light"] {
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #e9ecef;
  --bg-card: #ffffff;
  --bg-hover: #f1f3f4;
  
  --text-primary: #1a1a1a;
  --text-secondary: #4a4a4a;
  --text-muted: #6c757d;
  --text-accent: #0066cc;
  
  --border-color: #dee2e6;
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.08);
  --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.12);
  --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.16);
}

/* 基础样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  overflow-x: hidden;
}

/* 侧边栏容器 */
.sidebar-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
  position: relative;
}

/* 头部区域 */
.sidebar-header {
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-md);
  backdrop-filter: blur(10px);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.logo-icon {
  width: 32px;
  height: 32px;
  background: var(--gradient-primary);
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-actions {
  display: flex;
  gap: var(--spacing-xs);
}

/* 图标按钮 */
.icon-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: var(--bg-card);
  color: var(--text-secondary);
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  border: 1px solid var(--border-color);
}

.icon-btn:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.icon-btn:active {
  transform: translateY(0);
}

/* 主要内容区域 */
.sidebar-main {
  flex: 1;
  padding: var(--spacing-md);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  min-height: 0; /* 确保flex子元素可以收缩 */
}

/* 钉钉认证状态样式 */
.dingtalk-auth-status {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
}

.auth-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
}

.auth-icon {
  display: flex;
  align-items: center;
  color: #1890ff;
}

.auth-content {
  transition: all 0.3s ease;
}

/* 未认证状态 */
.auth-not-logged-in {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.auth-message {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}

.auth-status-text {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.auth-description {
  font-size: 12px;
  color: var(--text-secondary);
  line-height: 1.4;
}

.auth-actions {
  display: flex;
  align-items: center;
  gap: 6px;
}

.auth-login-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.auth-login-btn:hover {
  background: #40a9ff;
  transform: translateY(-1px);
}

.auth-login-btn:active {
  transform: translateY(0);
}

.auth-help-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  background: var(--bg-secondary);
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.auth-help-btn:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  transform: translateY(-1px);
}

.auth-help-btn:active {
  transform: translateY(0);
}

/* 已认证状态 */
.auth-logged-in {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.auth-user-info {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
  min-width: 0;
}

.auth-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  font-weight: 500;
  flex-shrink: 0;
}

.auth-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.auth-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
  min-width: 0;
  flex: 1;
}

.auth-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.auth-org {
  font-size: 12px;
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.auth-actions {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-shrink: 0;
}

.auth-settings-btn,
.auth-logout-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  background: var(--bg-secondary);
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.auth-settings-btn:hover,
.auth-logout-btn:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  transform: translateY(-1px);
}

.auth-logout-btn:hover {
  background: #ff4d4f;
  color: white;
}

.auth-settings-btn:active,
.auth-logout-btn:active {
  transform: translateY(0);
}

/* 页面信息 */
.page-info {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  box-shadow: var(--shadow-sm);
}

.page-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.page-url {
  font-size: 12px;
  color: var(--text-muted);
  margin-bottom: var(--spacing-sm);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.page-stats {
  display: flex;
  gap: var(--spacing-md);
}

.word-count {
  font-size: 12px;
  color: var(--text-accent);
  background: var(--bg-tertiary);
  padding: 2px 8px;
  border-radius: 12px;
}

/* 模板选择区域 */
.template-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.section-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
}

.template-select {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-primary);
  font-size: 14px;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.template-select:hover {
  border-color: var(--text-accent);
}

.template-select:focus {
  outline: none;
  border-color: var(--text-accent);
  box-shadow: 0 0 0 2px rgba(100, 255, 218, 0.2);
}

/* 操作按钮 */
.action-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.primary-btn {
  background: var(--gradient-primary);
  border: none;
  border-radius: var(--border-radius);
  padding: var(--spacing-md) var(--spacing-lg);
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.primary-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.primary-btn:active {
  transform: translateY(0);
}

.primary-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.secondary-btn {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-md) var(--spacing-lg);
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
}

.secondary-btn:hover {
  background: var(--bg-hover);
  border-color: var(--text-accent);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.secondary-btn:active {
  transform: translateY(0);
}

.secondary-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 结果区域 */
.result-section {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  animation: slideIn var(--transition-normal);
  max-height: 80vh; /* 增加最大高度为视口高度的80% */
  display: flex;
  flex-direction: column;
}

.result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-color);
}

.result-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.result-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.result-content {
  padding: var(--spacing-md);
  color: var(--text-primary);
  line-height: 1.7;
  font-size: 14px;
  overflow-y: auto; /* 允许垂直滚动 */
  overflow-x: hidden; /* 隐藏水平滚动 */
  flex: 1; /* 占用剩余空间 */
  max-height: calc(80vh - 120px); /* 增加最大高度，减去头部和底部的高度 */
  min-height: 200px; /* 设置最小高度确保内容可见 */
  word-wrap: break-word; /* 确保长文本能够换行 */
  word-break: break-word; /* 处理长单词 */
  white-space: pre-wrap; /* 保持空格和换行，但允许换行 */
}

.result-content h1, .result-content h2, .result-content h3 {
  margin-bottom: var(--spacing-sm);
  color: var(--text-accent);
}

.result-content ul, .result-content ol {
  margin-left: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
}

.result-content li {
  margin-bottom: var(--spacing-xs);
}

.result-meta {
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-tertiary);
  border-top: 1px solid var(--border-color);
  font-size: 12px;
  color: var(--text-muted);
  display: flex;
  justify-content: space-between;
}

/* 加载状态 */
.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-xl);
  text-align: center;
}

.loading-spinner {
  position: relative;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--text-accent);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 16px;
  color: var(--text-secondary);
  font-weight: 500;
}

.loading-progress {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: var(--bg-tertiary);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--gradient-accent);
  border-radius: 2px;
  transition: width var(--transition-normal);
  width: 0%;
}

.progress-text {
  font-size: 12px;
  color: var(--text-muted);
  text-align: center;
}

/* 错误状态 */
.error-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-xl);
  text-align: center;
}

.error-icon {
  color: #ff6b6b;
}

.error-message {
  font-size: 16px;
  color: var(--text-secondary);
}

/* 底部信息 */
.sidebar-footer {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-sm) var(--spacing-md);
}

.footer-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  font-size: 12px;
  color: var(--text-muted);
}

.divider {
  opacity: 0.5;
}

/* Toast 通知 */
.toast {
  position: fixed;
  top: var(--spacing-md);
  right: var(--spacing-md);
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  transform: translateX(100%);
  transition: transform var(--transition-normal);
}

.toast.show {
  transform: translateX(0);
}

.toast-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.toast-message {
  font-size: 14px;
  color: var(--text-primary);
}

/* 动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 滚动条样式 */
.sidebar-main::-webkit-scrollbar,
.result-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-main::-webkit-scrollbar-track,
.result-content::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: 3px;
}

.sidebar-main::-webkit-scrollbar-thumb,
.result-content::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
  transition: background var(--transition-fast);
}

.sidebar-main::-webkit-scrollbar-thumb:hover,
.result-content::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* 结果内容滚动指示器 */
.result-content {
  position: relative;
}

.result-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 10px;
  background: linear-gradient(to bottom, var(--bg-card), transparent);
  pointer-events: none;
  z-index: 1;
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.result-content::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 10px;
  background: linear-gradient(to top, var(--bg-card), transparent);
  pointer-events: none;
  z-index: 1;
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.result-content.scrollable-top::before {
  opacity: 1;
}

.result-content.scrollable-bottom::after {
  opacity: 1;
}

/* 滚动提示 */
.scroll-hint {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: 12px;
  z-index: 10;
  animation: scrollHintPulse 2s ease-in-out infinite;
  pointer-events: none;
}

.scroll-hint-text {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

@keyframes scrollHintPulse {
  0%, 100% { opacity: 0.7; transform: translateX(-50%) translateY(0); }
  50% { opacity: 1; transform: translateX(-50%) translateY(-2px); }
}

/* 响应式设计 */
@media (max-width: 400px) {
  .sidebar-container {
    font-size: 14px;
  }

  .header-content {
    padding: var(--spacing-sm);
  }

  .sidebar-main {
    padding: var(--spacing-sm);
    gap: var(--spacing-md);
  }
}

/* 按钮加载状态 */
.primary-btn.loading {
  position: relative;
  color: transparent;
}

.primary-btn.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.secondary-btn.loading {
  position: relative;
  color: transparent;
}

.secondary-btn.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid var(--text-accent);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 错误状态增强 */
.error-main {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-danger);
  margin-bottom: var(--spacing-sm);
}

.error-suggestion {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
  padding: var(--spacing-sm);
  background: rgba(244, 67, 54, 0.1);
  border-radius: var(--border-radius-sm);
  border-left: 3px solid var(--text-danger);
}

.error-details {
  font-size: 12px;
  color: var(--text-muted);
  font-family: monospace;
  background: var(--bg-tertiary);
  padding: var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  max-height: 100px;
  overflow-y: auto;
}

/* Toast 增强 */
.toast-icon-text {
  font-size: 16px;
  font-weight: bold;
  margin-right: var(--spacing-xs);
}

.toast.success {
  border-left: 4px solid var(--text-success);
}

.toast.error {
  border-left: 4px solid var(--text-danger);
}

.toast.warning {
  border-left: 4px solid var(--text-warning);
}

.toast.info {
  border-left: 4px solid var(--text-accent);
}

/* 错误动画 */
@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

/* 加载状态增强 */
.loading-section {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  margin: var(--spacing-md) 0;
  box-shadow: var(--shadow-sm);
}

.loading-spinner::before {
  content: '';
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border: 2px solid transparent;
  border-top: 2px solid var(--text-accent);
  border-radius: 50%;
  animation: spin 2s linear infinite;
  opacity: 0.3;
}

/* 进度条增强 */
.progress-bar {
  position: relative;
  overflow: visible;
}

.progress-fill {
  position: relative;
  background: var(--gradient-accent);
  box-shadow: 0 0 10px rgba(79, 172, 254, 0.3);
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  right: -2px;
  width: 4px;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 2px;
  animation: progressGlow 1s ease-in-out infinite alternate;
}

@keyframes progressGlow {
  0% { opacity: 0.5; transform: scaleY(0.8); }
  100% { opacity: 1; transform: scaleY(1); }
}

/* 结果区域增强 */
.result-section {
  position: relative;
  overflow: hidden;
}

.result-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 2px;
  background: var(--gradient-accent);
  animation: resultSlideIn 0.8s ease-out;
}

@keyframes resultSlideIn {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Markdown区域样式 */
.markdown-section {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  animation: slideIn var(--transition-normal);
  max-height: 85vh; /* 增加Markdown预览区域的最大高度 */
  display: flex;
  flex-direction: column;
}

.markdown-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-color);
}

.markdown-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.markdown-actions {
  display: flex;
  gap: var(--spacing-xs);
}

/* Markdown标签页 */
.markdown-tabs {
  display: flex;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.tab-btn {
  flex: 1;
  padding: var(--spacing-sm) var(--spacing-md);
  background: transparent;
  border: none;
  color: var(--text-muted);
  font-size: 14px;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
}

.tab-btn.active {
  color: var(--text-primary);
  background: var(--bg-card);
}

.tab-btn.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-primary);
}

.tab-btn:hover:not(.active) {
  color: var(--text-secondary);
  background: var(--bg-hover);
}

/* Markdown内容区域 */
.markdown-content {
  padding: var(--spacing-md);
  color: var(--text-primary);
  line-height: 1.7;
  font-size: 14px;
  overflow-y: auto;
  overflow-x: hidden; /* 隐藏水平滚动 */
  flex: 1;
  max-height: calc(85vh - 140px); /* 增加Markdown内容区域的最大高度 */
  min-height: 250px; /* 设置最小高度确保内容可见 */
  word-wrap: break-word; /* 确保长文本能够换行 */
  word-break: break-word; /* 处理长单词 */
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  color: var(--text-accent);
  margin: var(--spacing-md) 0 var(--spacing-sm) 0;
  font-weight: 600;
}

.markdown-content h1 { font-size: 24px; }
.markdown-content h2 { font-size: 20px; }
.markdown-content h3 { font-size: 18px; }
.markdown-content h4 { font-size: 16px; }
.markdown-content h5 { font-size: 14px; }
.markdown-content h6 { font-size: 12px; }

.markdown-content p {
  margin: var(--spacing-sm) 0;
}

.markdown-content ul,
.markdown-content ol {
  margin: var(--spacing-sm) 0;
  padding-left: var(--spacing-lg);
}

.markdown-content li {
  margin: var(--spacing-xs) 0;
}

.markdown-content blockquote {
  margin: var(--spacing-md) 0;
  padding: var(--spacing-sm) var(--spacing-md);
  border-left: 4px solid var(--text-accent);
  background: var(--bg-secondary);
  border-radius: var(--border-radius-sm);
  font-style: italic;
}

.markdown-content code {
  background: var(--bg-secondary);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: var(--text-accent);
}

.markdown-content pre {
  background: var(--bg-secondary);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-sm);
  overflow-x: auto;
  margin: var(--spacing-md) 0;
}

.markdown-content pre code {
  background: none;
  padding: 0;
  color: var(--text-primary);
}

.markdown-content a {
  color: var(--text-accent);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color var(--transition-fast);
}

.markdown-content a:hover {
  border-bottom-color: var(--text-accent);
}

/* Markdown源码编辑区域 */
.markdown-source {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.markdown-source textarea {
  flex: 1;
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border: none;
  color: var(--text-primary);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.6;
  resize: none;
  outline: none;
  min-height: 300px;
}

.markdown-source textarea::placeholder {
  color: var(--text-muted);
}

/* Markdown元信息 */
.markdown-meta {
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  font-size: 12px;
  color: var(--text-muted);
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

/* 加载状态增强 */
.primary-btn.loading,
.secondary-btn.loading {
  pointer-events: none;
}

.primary-btn.loading::before,
.secondary-btn.loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* 响应式增强 */
@media (max-width: 350px) {
  .form-actions {
    flex-direction: column;
  }

  .result-actions,
  .markdown-actions {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .icon-btn {
    width: 100%;
    justify-content: center;
  }

  .markdown-meta {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
}

/* ==================== UI权限控制样式 ==================== */

/* 功能区域容器 */
.feature-container {
  position: relative;
  transition: all var(--transition-normal);
}

/* 认证遮罩层 */
.auth-required-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
  border-radius: var(--border-radius);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-normal);
}

/* 认证提示内容 */
.auth-required-content {
  text-align: center;
  color: white;
  padding: var(--spacing-lg);
  max-width: 280px;
}

.auth-icon {
  font-size: 48px;
  margin-bottom: var(--spacing-md);
  opacity: 0.9;
}

.auth-message h4 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 16px;
  font-weight: 600;
  color: white;
}

.auth-message p {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
  line-height: 1.4;
}

/* 权限禁用状态的通用样式 */
.permission-disabled {
  opacity: 0.6 !important;
  pointer-events: none !important;
  cursor: not-allowed !important;
  position: relative;
  transition: all var(--transition-normal);
}

/* 需要认证的元素样式 */
.auth-required {
  transition: all var(--transition-normal);
}

/* 权限禁用的按钮样式 */
button.permission-disabled,
.primary-btn.permission-disabled,
.secondary-btn.permission-disabled {
  background: var(--bg-tertiary) !important;
  color: var(--text-muted) !important;
  border-color: var(--border-color) !important;
  box-shadow: none !important;
  filter: grayscale(0.5);
}

/* 权限禁用的选择器样式 */
select.permission-disabled {
  background: var(--bg-tertiary) !important;
  color: var(--text-muted) !important;
  border-color: var(--border-color) !important;
  filter: grayscale(0.5);
}

/* 权限禁用的区域样式 */
section.permission-disabled {
  background: var(--bg-tertiary) !important;
  border: 1px dashed var(--border-color) !important;
  filter: grayscale(0.3);
}

/* 功能容器在未认证状态下的样式 */
.feature-container.auth-locked {
  filter: blur(1px) grayscale(0.3);
  opacity: 0.7;
}

/* 权限提示工具提示 */
.permission-disabled[title]:hover::after {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
  margin-bottom: 5px;
}

/* 认证成功状态的动画效果 */
.permission-enabled {
  animation: permission-unlock 0.6s ease-out;
}

.feature-container.permission-unlocked {
  animation: feature-unlock 0.8s ease-out;
}

@keyframes permission-unlock {
  0% {
    opacity: 0.6;
    transform: scale(0.98);
    filter: blur(1px);
  }
  50% {
    transform: scale(1.01);
  }
  100% {
    opacity: 1;
    transform: scale(1);
    filter: blur(0);
  }
}

@keyframes feature-unlock {
  0% {
    filter: blur(1px) grayscale(0.3);
    opacity: 0.7;
  }
  50% {
    filter: blur(0.5px) grayscale(0.1);
    opacity: 0.9;
  }
  100% {
    filter: blur(0) grayscale(0);
    opacity: 1;
  }
}

/* 认证遮罩层的进入和退出动画 */
.auth-required-overlay.fade-in {
  animation: overlay-fade-in 0.3s ease-out;
}

.auth-required-overlay.fade-out {
  animation: overlay-fade-out 0.3s ease-out forwards;
}

@keyframes overlay-fade-in {
  from {
    opacity: 0;
    backdrop-filter: blur(0);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(4px);
  }
}

@keyframes overlay-fade-out {
  from {
    opacity: 1;
    backdrop-filter: blur(4px);
  }
  to {
    opacity: 0;
    backdrop-filter: blur(0);
  }
}

/* 浅色主题下的权限控制样式调整 */
[data-theme="light"] .auth-required-overlay {
  background: rgba(255, 255, 255, 0.9);
}

[data-theme="light"] .auth-required-content {
  color: var(--text-primary);
}

[data-theme="light"] .auth-message h4,
[data-theme="light"] .auth-message p {
  color: var(--text-primary);
}

/* 响应式设计 */
@media (max-width: 480px) {
  .auth-required-content {
    padding: var(--spacing-md);
  }

  .auth-icon {
    font-size: 36px;
  }

  .auth-message h4 {
    font-size: 14px;
  }

  .auth-message p {
    font-size: 12px;
  }
}
